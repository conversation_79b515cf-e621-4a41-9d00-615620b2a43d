import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:remixicon/remixicon.dart';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/colors.dart';

class CustomBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const CustomBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final navBarWidth = screenWidth > 360 ? 328.0 : screenWidth - 32;
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    return Container(
      margin: const EdgeInsets.only(bottom: 28, left: 16, right: 16),
      width: navBarWidth,
      height: 72,
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(16),
        border:
            Border.all(color: colors.strokeColor.withOpacity(0.10), width: 1),
        // boxShadow: [
        //   BoxShadow(
        //     color: colors.strokeColor.withOpacity(0.05),
        //     blurRadius: 10,
        //     offset: const Offset(0, 2),
        //   ),
        // ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildNavItem(
            context: context,
            customIcon: SvgPicture.asset(
              'assets/icons/home.svg',
              colorFilter: ColorFilter.mode(
                currentIndex == 0
                    ? colors.primary
                    : colors.secondaryText.withOpacity(0.7),
                BlendMode.srcIn,
              ),
            ),
            label: Labels.home,
            index: 0,
          ),
          _buildNavItem(
            context: context,
            customIcon: SvgPicture.asset(
              'assets/icons/chat.svg',
              colorFilter: ColorFilter.mode(
                currentIndex == 1
                    ? colors.primary
                    : colors.secondaryText.withOpacity(0.7),
                BlendMode.srcIn,
              ),
            ),
            label: Labels.chat,
            index: 1,
          ),
          _buildNavItem(
            context: context,
            customIcon: SvgPicture.asset(
              'assets/icons/tasks.svg',
              colorFilter: ColorFilter.mode(
                currentIndex == 2
                    ? colors.primary
                    : colors.secondaryText.withOpacity(0.7),
                BlendMode.srcIn,
              ),
            ),
            label: Labels.tasks,
            index: 2,
          ),
          _buildNavItem(
            context: context,
            customIcon: SvgPicture.asset(
              'assets/icons/profile-circle.svg',
              colorFilter: ColorFilter.mode(
                currentIndex == 3
                    ? colors.primary
                    : colors.secondaryText.withOpacity(0.7),
                BlendMode.srcIn,
              ),
            ),
            label: Labels.profile,
            index: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    IconData? icon,
    required String label,
    required int index,
    Widget? customIcon,
  }) {
    final isSelected = currentIndex == index;
    final color = isSelected
        ? colors(context).primary
        : colors(context).onSurfaceVariant.withOpacity(0.37);

    return InkWell(
      onTap: () => onTap(index),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            customIcon ??
                Icon(
                  icon!,
                  color: color,
                  size: 22,
                ),
            Container(
              margin: const EdgeInsets.only(top: 4),
              child: Text(
                label,
                style: TextStyle(
                  color: color,
                  fontSize: 12,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
