import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:remixicon/remixicon.dart';

import '../../../util/notification/push_notification.dart';
import '../../../util/ui/popups.dart';

class NotificationsScreen extends ConsumerStatefulWidget {
  const NotificationsScreen({super.key});

  @override
  ConsumerState<NotificationsScreen> createState() =>
      _NotificationsScreenState();
}

class _NotificationsScreenState extends ConsumerState<NotificationsScreen> {
  PermissionStatus _permissionStatus = PermissionStatus.denied;
  bool _allowNotif = false;
  @override
  void initState() {
    _check();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final switchState = ref.watch(pushNotificationServiceProvider);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        actions: [
          IconButton(
            onPressed: () async {
              await openAppSettings();
            },
            icon: const Icon(Remix.list_settings_line),
          ),
          if (kDebugMode)
            IconButton(
              onPressed: () async {
                print(await ref
                    .read(pushNotificationServiceProvider.notifier)
                    .getDeviceToken());
              },
              icon: const Icon(Remix.device_line),
            )
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(height: 20),
          SwitchListTile.adaptive(
            visualDensity: VisualDensity.compact,
            dense: true,
            secondary: const Icon(
              Icons.notifications_rounded,
              size: 26,
            ),
            title: const Text(
              'Show Push Notifications',
              // style: AppTextStyle.menuTitle,
            ),
            subtitle: Text(
              switchState
                  ? 'You\'ll receive push notifications.'
                  : 'You won\'t receive push notifications.',
              // style: AppTextStyle.menuSubtitle.copyWith(fontSize: 14),
            ).animate().fadeIn(),
            contentPadding: const EdgeInsets.fromLTRB(24, 0, 20, 0),
            // activeColor: AppColors.primary,
            value: switchState,
            onChanged: _handleToggle,
          ),
        ],
      ),
    );
  }

  void _check() async {
    final status = await Permission.notification.status;
    setState(() {
      // _permissionStatus = status;
    });
  }

  void _handleToggle(bool value) async {
    if (value) {
      ref
          .read(pushNotificationServiceProvider.notifier)
          .enableNotifications((errorMsg) async {
        if (context.mounted) {
          showAppSnackbar(context, 'Problems with notifications',
              errorMsg ?? 'Please try again later');
        }
        if (errorMsg ==
            'Please allow the app to show notifications on the next screen.') {
          await Future.delayed(2.seconds).then((_) async {
            await openAppSettings();
          });
        }
      });
    } else {
      ref.read(pushNotificationServiceProvider.notifier).disableNotifications();
    }
  }
}
