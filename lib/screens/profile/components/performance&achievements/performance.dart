import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:remixicon/remixicon.dart';

class Performance extends StatefulWidget {
  const Performance({super.key});

  @override
  State<Performance> createState() => _PerformanceState();
}

class _PerformanceState extends State<Performance> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: Column(
        children: [
          Text(
            Labels.performance,
            style: textStyles.headline4.copyWith(
              color: colors.secondaryText,
            ),
          ),
          // Overtime Card
          Container(
            margin: const EdgeInsets.only(top: 6),
            child: Container(
              width: MediaQuery.of(context).size.width - 32,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: colors.primaryVariant,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  width: 1,
                  color: colors.primaryVariant,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Remix.time_line,
                        color: colors.primary,
                        size: 20,
                      ),
                      Container(
                        margin: const EdgeInsets.only(left: 8),
                      ),
                      Text(
                        Labels.overTime,
                        style: textStyles.body2.copyWith(
                          color: colors.primary,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    margin: const EdgeInsets.only(top: 8, left: 2),
                    child: Text(
                      '12 Hours this month',
                      style: textStyles.headline3.copyWith(
                        color: colors.primaryText,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Performance Metrics Grid
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: MediaQuery.of(context).size.width - 32,
            child: Column(
              children: [
                // First Row
                Row(
                  children: [
                    Expanded(
                      child: _buildMetricCard(
                        context,
                        '45',
                        Labels.completedTasks,
                        colors.success, // Green color
                        colors,
                        BoxDecoration(
                          color: colors.successContainer,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        textStyles,
                      ),
                    ),
                    Container(margin: const EdgeInsets.only(left: 8)),
                    Expanded(
                      child: _buildMetricCard(
                        context,
                        '5',
                        Labels.timeOffTaken,
                        colors.warning, // Orange color
                        colors,
                        BoxDecoration(
                          color: colors.warningContainer,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        textStyles,
                        subtitle: 'Days',
                      ),
                    ),
                  ],
                ),
                Container(margin: const EdgeInsets.only(top: 8)),
                // Second Row
                Row(
                  children: [
                    Expanded(
                      child: _buildMetricCard(
                        context,
                        '4',
                        Labels.expenses,
                        colors.info, // Blue color
                        colors,
                        BoxDecoration(
                          color: colors.infoContainer,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        textStyles,
                      ),
                    ),
                    Container(margin: const EdgeInsets.only(left: 8)),
                    Expanded(
                      child: _buildMetricCard(
                        context,
                        '15',
                        Labels.holidays,
                        colors.primary, // Light blue color
                        colors,
                        BoxDecoration(
                          color: colors.primaryVariant,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        textStyles,
                        subtitle: 'Days',
                      ),
                    ),
                  ],
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildMetricCard(
    BuildContext context,
    String value,
    String title,
    Color numberColor,
    AppColors colors,
    BoxDecoration decoration,
    TextStyles textStyles, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: decoration,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                value,
                style: textStyles.headline.copyWith(
                  color: numberColor,
                ),
              ),
              if (subtitle != null) ...[
                Container(margin: const EdgeInsets.only(left: 4)),
                Text(
                  subtitle,
                  style: textStyles.body2.copyWith(
                    color: colors.tertiaryText,
                  ),
                ),
              ],
            ],
          ),
          Container(margin: const EdgeInsets.only(top: 8)),
          Text(
            title,
            style: textStyles.body2.copyWith(
              color: colors.secondaryText,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
