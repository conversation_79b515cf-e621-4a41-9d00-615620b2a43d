import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

class Feedback extends StatefulWidget {
  const Feedback({super.key});

  @override
  State<StatefulWidget> createState() {
    return _FeedbackState();
  }
}

class _FeedbackState extends State<Feedback> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Column(
        children: [
          Text(
            Labels.feedback,
            style: textStyles.headline4.copyWith(
              color: colors.secondaryText,
            ),
          ),
          Container(
            margin: const EdgeInsets.only(top: 8),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              decoration: BoxDecoration(
                color: colors.backgroundContainer,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: colors.primaryVariant,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with manager's feedback title and time
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          Labels.managersFeedback,
                          style: textStyles.body3.copyWith(
                            color: colors.primaryText,
                          ),
                        ),
                      ),
                      Text(
                        Labels.feedbackTime,
                        style: textStyles.body3.copyWith(
                          color: colors.tertiaryText,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    margin: const EdgeInsets.only(top: 12),
                  ),
                  // Feedback message
                  Text(
                    Labels.feedbackMessage,
                    style: textStyles.body2.copyWith(
                      color: colors.secondaryText,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
