import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

class Name extends StatefulWidget {
  const Name({super.key});

  @override
  State<Name> createState() => _NameState();
}

class _NameState extends State<Name> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Container(
      height: 48,
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Stack(
                children: [
                  Container(
                    height: 40,
                    width: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: colors.tertiaryText,
                        width: 1,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: const ImageContainer(
                        url: null,
                        placeholderAsset: 'assets/images/person.png',
                        height: 40,
                        width: 40,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  Positioned(
                    right: 0,
                    bottom: 0,
                    child: Container(
                      height: 18,
                      width: 18,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: colors.primary,
                        border: Border.all(
                          color: colors.primaryText,
                          width: 2,
                        ),
                      ),
                      child: Icon(
                        Icons.edit,
                        size: 8,
                        color: colors.primaryText,
                      ),
                    ),
                  ),
                ],
              ),
              Container(
                width: 12,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    Labels.name,
                    style: textStyles.body3.copyWith(
                      color: colors.primaryText,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    Labels.role,
                    style: textStyles.body3.copyWith(
                      color: colors.primaryText,
                    ),
                  ),
                ],
              ),
            ],
          ),
          Container(
            height: 30,
            width: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: colors.strokeColor,
                width: 1,
              ),
              color: colors.background,
            ),
            child: Center(
              child: Text(
                Labels.designation,
                style: textStyles.body3.copyWith(
                  color: colors.primaryText,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
