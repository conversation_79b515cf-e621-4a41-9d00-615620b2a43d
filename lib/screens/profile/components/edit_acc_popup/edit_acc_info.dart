import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/profile/components/edit_acc_popup/acc_info.dart';
import 'package:ako_basma/screens/profile/components/edit_acc_popup/not_editable.dart';
import 'package:ako_basma/screens/profile/components/edit_acc_popup/other_info.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

class EditAccInfo extends StatefulWidget {
  const EditAccInfo({super.key});

  @override
  State<EditAccInfo> createState() => _EditAccInfoState();
}

class _EditAccInfoState extends State<EditAccInfo> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // AppBar with back button and title
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        Icons.arrow_back_ios_new,
                        color: colors.secondaryText,
                        size: 20,
                      ),
                    ),
                  ),
                  Container(margin: const EdgeInsets.only(left: 16)),
                  Text(
                    Labels.editAccountInfo,
                    style: textStyles.headline4.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),
            const AccInfo(),
            const NotEditable(),
            const OtherInfo(),
            Container(
              margin: const EdgeInsets.only(bottom: 24),
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  backgroundColor: colors.primary,
                  foregroundColor: colors.primaryText,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: Text(
                  Labels.save,
                  style: textStyles.headline2.copyWith(
                    color: colors.primaryText,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
