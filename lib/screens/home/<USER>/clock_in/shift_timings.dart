import 'package:flutter/material.dart';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:remixicon/remixicon.dart';

class ShiftTimings extends StatelessWidget {
  const ShiftTimings({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: _buildTimeCard(
            context: context,
            icon: Remix.login_circle_line,
            iconColor: colors.success,
            label: Labels.startWork,
            time: '8:00 AM', // get the actual time later
          ),
        ),
        Container(
          width: 12,
        ),
        Expanded(
          child: _buildTimeCard(
            context: context,
            icon: Remix.logout_circle_r_line,
            iconColor: colors.primary,
            label: Labels.endWork,
            time: '4:00 PM', // get the actual time later
          ),
        ),
      ],
    );
  }

  Widget _buildTimeCard({
    required BuildContext context,
    required dynamic icon,
    required Color iconColor,
    required String label,
    required String time,
  }) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Container(
      width: MediaQuery.of(context).size.width * 0.4,
      padding: const EdgeInsets.fromLTRB(8, 4, 8, 4),
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colors.primaryVariant,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: iconColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: iconColor,
                  size: 18,
                ),
              ),
              Container(
                margin: const EdgeInsets.only(left: 4),
                child: Text(
                  label,
                  style: textStyles.body3.copyWith(color: colors.secondaryText),
                ),
              ),
            ],
          ),
          Container(
            margin: const EdgeInsets.only(top: 4, left: 4),
            child: Text(
              time,
              style: textStyles.body.copyWith(color: colors.primaryText),
            ),
          ),
        ],
      ),
    );
  }
}
