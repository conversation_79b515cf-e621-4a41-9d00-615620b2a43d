import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

class ClockOutButton extends StatefulWidget {
  final VoidCallback? onPressed;

  const ClockOutButton({
    super.key,
    this.onPressed,
  });

  @override
  State<ClockOutButton> createState() => _ClockOutButtonState();
}

class _ClockOutButtonState extends State<ClockOutButton> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Column(
      children: [
        Transform(
          alignment: Alignment.center,
          transform: Matrix4.rotationZ(
            45 * math.pi / 180, // 45 degrees rotation
          ),
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 28, horizontal: 28),
            width: 100,
            height: 100,
            child: ElevatedButton(
              onPressed: widget.onPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: colors.error,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.all(4),
              ),
              child: Center(
                child: Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.rotationZ(
                    -45 * math.pi / 180, // Counter-rotate content
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.access_time,
                          color: colors.primaryText, size: 33),
                      Container(
                        margin: const EdgeInsets.only(top: 8),
                        child: Text(
                          Labels.clockOut,
                          style: textStyles.body.copyWith(
                            color: colors.primaryText,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
