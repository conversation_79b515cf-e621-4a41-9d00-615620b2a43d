import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

class SwitchButton extends StatefulWidget {
  const SwitchButton({
    super.key,
    required this.label,
    required this.onTap,
  });

  final String label;
  final Function(bool isFirstSelected) onTap;

  @override
  State<SwitchButton> createState() => _SwitchButtonState();
}

class _SwitchButtonState extends State<SwitchButton> {
  bool _isFirstSelected = true;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final screenWidth = MediaQuery.of(context).size.width;

    // styling for buttons
    const double buttonHeight = 35.0;
    const double borderRadius = 6.0;
    const double borderWidth = 1.0;
    final Color selectedBgColor = colors.primaryVariant;
    final Color unselectedBgColor = colors.background;
    final Color selectedTextColor = colors.primary;
    final Color unselectedTextColor = colors.secondaryText;
    final Color unselectedBorderColor = colors.strokeColor;

    return Container(
      width: screenWidth - 32,
      height: 48,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        border: Border.all(
          color: colors.primaryVariant,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
        color: colors.backgroundContainer,
      ),
      padding: const EdgeInsets.all(4),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _isFirstSelected = true;
                });
                widget.onTap(_isFirstSelected);
              },
              child: Container(
                height: buttonHeight,
                decoration: BoxDecoration(
                  color: _isFirstSelected ? selectedBgColor : unselectedBgColor,
                  borderRadius: BorderRadius.circular(borderRadius),
                  border: Border.all(
                    color: _isFirstSelected
                        ? selectedBgColor
                        : unselectedBorderColor,
                    width: borderWidth,
                  ),
                ),
                child: Center(
                  child: Text(
                    widget.label,
                    textAlign: TextAlign.center,
                    style: textStyles.body.copyWith(
                      color: _isFirstSelected
                          ? selectedTextColor
                          : unselectedTextColor,
                    ),
                  ),
                ),
              ),
            ),
          ),
          Container(
            width: 8,
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _isFirstSelected = false;
                });
                widget.onTap(_isFirstSelected);
              },
              child: Container(
                height: buttonHeight,
                decoration: BoxDecoration(
                  color:
                      !_isFirstSelected ? selectedBgColor : unselectedBgColor,
                  borderRadius: BorderRadius.circular(borderRadius),
                  border: Border.all(
                    color: !_isFirstSelected
                        ? selectedBgColor
                        : unselectedBorderColor,
                    width: borderWidth,
                  ),
                ),
                child: Center(
                  child: Text(
                    Labels.workspace,
                    textAlign: TextAlign.center,
                    style: textStyles.body.copyWith(
                      color: !_isFirstSelected
                          ? selectedTextColor
                          : unselectedTextColor,
                    ),
                    // TODO: check for the font size and font weight
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
