import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

class RequestLeavePopup extends StatefulWidget {
  final VoidCallback? onCancel;
  final VoidCallback? onSave;

  const RequestLeavePopup({
    super.key,
    this.onCancel,
    this.onSave,
  });

  @override
  State<RequestLeavePopup> createState() => _RequestLeavePopupState();
}

class _RequestLeavePopupState extends State<RequestLeavePopup> {
  final TextEditingController fromTimeController =
      TextEditingController(text: '8:00 AM');
  final TextEditingController toTimeController =
      TextEditingController(text: '8:00 AM');

  final List<String> leaveTypes = [
    'Sick Leave',
    'Vacation',
    'Family Emergency'
  ];
  String selectedLeaveType = '';
  bool isDropdownOpen = false;
  bool isAllDay = false;

  @override
  void dispose() {
    fromTimeController.dispose();
    toTimeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Container(
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      // height: 333,
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                Labels.requestLeave,
                style: textStyles.headline4.copyWith(
                  color: colors.primary,
                ),
              ),
            ),
          ),

          // dropdown button
          Container(
            padding: const EdgeInsets.fromLTRB(10, 8, 10, 6),
            child: Column(
              children: [
                GestureDetector(
                  onTap: () {
                    setState(() {
                      // isDropdownOpen = !isDropdownOpen;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          selectedLeaveType.isEmpty
                              ? 'Leave Type'
                              : selectedLeaveType,
                          style: textStyles.body2.copyWith(
                            color: selectedLeaveType.isEmpty
                                ? colors.tertiaryText
                                : colors.primaryText,
                          ),
                        ),
                        Icon(
                          Icons.keyboard_arrow_down_rounded,
                          color: colors.primary,
                        ),
                      ],
                    ),
                  ),
                ),
                // below code is not specified in the figma designs but maybe for future reference

                if (isDropdownOpen)
                  Container(
                    margin: const EdgeInsets.only(top: 4),
                    constraints: BoxConstraints(maxHeight: 150),
                    decoration: BoxDecoration(
                      color: colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: colors.strokeColor,
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ListView.builder(
                      shrinkWrap: true,
                      padding: EdgeInsets.zero,
                      itemCount: leaveTypes.length,
                      itemBuilder: (context, index) {
                        return InkWell(
                          onTap: () {
                            setState(() {
                              selectedLeaveType = leaveTypes[index];
                              isDropdownOpen = false;
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                            child: Text(
                              leaveTypes[index],
                              style: textStyles.body2.copyWith(
                                color: colors.primaryText,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
              ],
            ),
          ),

          // toggle switch
          Container(
            padding: const EdgeInsets.fromLTRB(10, 6, 10, 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  Labels.allDay,
                  style: textStyles.body2.copyWith(
                    color: colors.primaryText,
                  ),
                ),
                Switch(
                  value: isAllDay,
                  onChanged: (value) {
                    setState(() {
                      isAllDay = value;
                    });
                  },
                  activeColor: colors.primary,
                  inactiveThumbColor: colors.background,
                  thumbColor: WidgetStateProperty.resolveWith((states) {
                    if (states.contains(WidgetState.selected)) {
                      return colors.primary;
                    }
                    return colors.background;
                  }),
                  inactiveTrackColor: colors.backgroundContainer,
                  trackOutlineColor: WidgetStateProperty.resolveWith(
                    (states) => colors.strokeColor,
                  ),
                  trackOutlineWidth: WidgetStateProperty.all(0.5),
                ),
              ],
            ),
          ),

          //  from and to date
          Container(
            padding: const EdgeInsets.fromLTRB(10, 8, 10, 8),
            child: Column(
              children: [
                // From time field
                Container(
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colors.strokeColor,
                      width: 1,
                    ),
                  ),
                  child: TextField(
                    controller: fromTimeController,
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      border: InputBorder.none,
                      labelText: 'From',
                      floatingLabelBehavior: FloatingLabelBehavior.auto,
                      labelStyle: textStyles.body2.copyWith(
                        color: colors.tertiaryText,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: colors.strokeColor,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: colors.strokeColor),
                      ),
                    ),
                    style: textStyles.body2.copyWith(
                      color: colors.primaryText,
                    ),
                    readOnly:
                        true, // Make it read-only as we'll use a time picker
                    onTap: () async {
                      // Show time picker when tapped
                      final TimeOfDay? picked = await showTimePicker(
                        context: context,
                        initialTime: TimeOfDay.now(),
                      );
                      if (picked != null) {
                        setState(() {
                          // Format the time as "8:00 AM"
                          final hour = picked.hourOfPeriod == 0
                              ? 12
                              : picked.hourOfPeriod;
                          final period =
                              picked.period == DayPeriod.am ? 'AM' : 'PM';
                          fromTimeController.text =
                              '$hour:${picked.minute.toString().padLeft(2, '0')} $period';
                        });
                      }
                    },
                  ),
                ),

                const SizedBox(height: 8),

                // To time field
                Container(
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colors.strokeColor,
                      width: 1,
                    ),
                  ),
                  child: TextField(
                    controller: toTimeController,
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      border: InputBorder.none,
                      labelText: 'To',
                      floatingLabelBehavior: FloatingLabelBehavior.auto,
                      labelStyle: textStyles.body2.copyWith(
                        color: colors.tertiaryText,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: colors.strokeColor,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: colors.primary),
                      ),
                    ),
                    style: textStyles.body2.copyWith(
                      color: colors.primaryText,
                    ),
                    readOnly: true,
                    onTap: () async {
                      // Show time picker when tapped
                      final TimeOfDay? picked = await showTimePicker(
                        context: context,
                        initialTime: TimeOfDay.now(),
                      );
                      if (picked != null) {
                        setState(() {
                          // Format the time as "8:00 AM"
                          final hour = picked.hourOfPeriod == 0
                              ? 12
                              : picked.hourOfPeriod;
                          final period =
                              picked.period == DayPeriod.am ? 'AM' : 'PM';
                          toTimeController.text =
                              '$hour:${picked.minute.toString().padLeft(2, '0')} $period';
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
          ),

          // Buttons
          Container(
            padding: const EdgeInsets.fromLTRB(10, 8, 10, 16),
            child: Container(
              height: 40,
              width: double.infinity,
              child: ElevatedButton(
                onPressed: widget.onSave ?? () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colors.primary,
                  foregroundColor: colors.primaryText,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  Labels.submit,
                  style: textStyles.headline4.copyWith(
                    color: colors.primaryText,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
