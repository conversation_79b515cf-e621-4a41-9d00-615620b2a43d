import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:flutter/material.dart';

class ProjectSelectionDialog extends StatefulWidget {
  final int selectedIndex;
  final List<String> projects;
  final Function(int) onSelected;

  const ProjectSelectionDialog({
    super.key,
    required this.selectedIndex,
    required this.projects,
    required this.onSelected,
  });

  @override
  State<ProjectSelectionDialog> createState() => _ProjectSelectionDialogState();
}

class _ProjectSelectionDialogState extends State<ProjectSelectionDialog> {
  late int _selectedIndex;

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.selectedIndex;
  }

  @override
  Widget build(BuildContext context) {
    final theme = colors(context);

    return Dialog(
      backgroundColor: theme.surfaceContainer,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(16),
        width: MediaQuery.of(context).size.width * 0.9,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                Labels.projects,
                style: TextStyle(
                  color: theme.primary,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ),
            Container(
              margin: const EdgeInsets.only(top: 16),
            ),
            ...List.generate(widget.projects.length, (index) {
              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: theme.surface,
                  border: Border.all(color: theme.outline, width: 1),
                ),
                child: RadioListTile<int>(
                  value: index,
                  groupValue: _selectedIndex,
                  onChanged: (val) {
                    setState(() => _selectedIndex = val!);
                  },
                  title: Text(
                    widget.projects[index],
                    style: TextStyle(
                      color: theme.onSurface,
                      fontSize: 16,
                    ),
                  ),
                  activeColor: theme.primary,
                  controlAffinity: ListTileControlAffinity.leading,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                ),
              );
            }),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(Labels.cancel.toLowerCase()),
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(left: 16),
                ),
                Expanded(
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.primary,
                    ),
                    onPressed: () {
                      widget.onSelected(_selectedIndex);
                      Navigator.pop(context);
                    },
                    child: const Text(
                      Labels.save,
                      style: TextStyle(fontSize: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
