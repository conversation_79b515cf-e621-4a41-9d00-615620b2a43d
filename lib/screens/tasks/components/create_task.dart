import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/tasks/components/add_task_popup.dart';
import 'package:ako_basma/screens/tasks/components/create_project.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class CreateTask extends StatelessWidget {
  final VoidCallback? onBack;
  final VoidCallback? onTaskCreated;

  const CreateTask({
    super.key,
    this.onBack,
    this.onTaskCreated,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildAppBar(context),
        Expanded(
          child: _buildBody(context),
        ),
      ],
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return Container(
      color: colors(context).surfaceContainer,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppBar(
            backgroundColor: colors(context).surfaceContainer,
            title: Row(
              children: [
                Text(
                  Labels.allProjects,
                  style: TextStyle(
                    color: colors(context).onSurface.withOpacity(0.87),
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: colors(context).primary.withOpacity(0.87),
                  size: 20,
                ),
              ],
            ),
            titleSpacing: 0,
            leadingWidth: 20,
            leading: Container(
              margin: const EdgeInsets.only(left: 20),
            ),
            actions: [
              Container(
                margin: const EdgeInsets.only(right: 16),
                decoration: BoxDecoration(
                  color: colors(context).brightness == Brightness.dark
                      ? DesignColors.darkBackgroundContainer
                      : DesignColors.lightBackgroundContainer,
                  borderRadius: BorderRadius.circular(6.67),
                  border: Border.all(
                    color: colors(context).brightness == Brightness.dark
                        ? DesignColors.darkStrokeColor
                        : DesignColors.lightStrokeColor,
                    width: 1,
                  ),
                ),
                child: IconButton(
                  onPressed: () => _showAddTaskPopup(context),
                  icon: Icon(
                    Icons.add,
                    color: colors(context).primary,
                  ),
                ),
              ),
            ],
          ),
          Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.only(left: 20, bottom: 8),
            child: Text(
              Labels.manageProjects,
              style: TextStyle(
                color: colors(context).onSurface.withOpacity(0.5),
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddTaskPopup(BuildContext context) {
    showAdaptivePopup(
      context,
      (ctx, sc) => AddTaskPopup(
        onBack: () => Navigator.pop(ctx),
        onTaskCreated: () {
          Navigator.pop(ctx);
          // Pass the callback up to the Tasks screen
          if (onTaskCreated != null) {
            onTaskCreated!();
          }
        },
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );
  }

  Widget _buildBody(BuildContext context) {
    return Container(
      color: colors(context).surfaceContainer,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Empty state icon
            Image.asset(
              'assets/images/create_task.png',
              width: 64,
              height: 64,
              color: colors(context).onSurface.withOpacity(0.6),
            ),
            Container(
              margin: const EdgeInsets.only(top: 16),
            ),
            Text(
              Labels.noTasksFound,
              style: TextStyle(
                color: colors(context).brightness == Brightness.dark
                    ? DesignColors.darkPrimaryText
                    : DesignColors.lightPrimaryText,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            Container(
              margin: const EdgeInsets.only(top: 8),
            ),
            Text(
              Labels.pleaseCreateATask,
              style: TextStyle(
                color: colors(context).brightness == Brightness.dark
                    ? DesignColors.darkSecondaryText
                    : DesignColors.lightSecondaryText,
                fontSize: 14,
              ),
            ),
            Container(
              margin: const EdgeInsets.only(top: 24),
            ),
            Container(
              margin: const EdgeInsets.symmetric(
                  horizontal:
                      120), // i don't think this is a good way to give width to the button
              child: ElevatedButton.icon(
                onPressed: () => _showAddTaskPopup(context),
                icon:
                    Icon(Icons.add, size: 20, color: colors(context).onPrimary),
                label: Text(
                  Labels.createTask,
                  style: textStyles(context).labelLarge?.copyWith(
                        color: colors(context).onPrimary,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colors(context).primary,
                  foregroundColor: colors(context).onPrimary,
                  padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                  minimumSize: const Size(double.infinity, 48),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
