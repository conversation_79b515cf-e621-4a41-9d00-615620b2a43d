import 'dart:io';

import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter_svg/svg.dart';

class AddTaskPopup extends StatefulWidget {
  final VoidCallback? onBack;
  final VoidCallback? onTaskCreated;

  const AddTaskPopup({
    super.key,
    this.onBack,
    this.onTaskCreated,
  });

  @override
  State<AddTaskPopup> createState() => _AddTaskPopupState();
}

class _AddTaskPopupState extends State<AddTaskPopup> {
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  File? selectedFile;

  @override
  void dispose() {
    titleController.dispose();
    descriptionController.dispose();
    super.dispose();
  }

  // file picker extensions
  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'pdf', 'png', 'jpeg'],
      );

      if (result != null &&
          result.files.isNotEmpty &&
          result.files.single.path != null) {
        setState(() {
          selectedFile = File(result.files.single.path!);
        });
        print("File selected: ${result.files.single.path}");
      } else {
        print("No file selected or file path is null");
      }
    } catch (e) {
      print("Error picking file: $e");
    }
  }

  void _handleAddTask() {
    // Create a new task with the entered data
    if (titleController.text.isNotEmpty) {
      // Close the popup
      Navigator.pop(context);

      // Call the callback with task data
      if (widget.onTaskCreated != null) {
        widget.onTaskCreated!();
      }
    } else {
      // Show validation error
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a task title')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: colors(context).surfaceContainer,
      body: SafeArea(
        child: Column(
          children: [
            // Header with back button and title
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: widget.onBack ?? () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: colors(context).brightness == Brightness.dark
                            ? DesignColors.darkBackgroundContainer
                            : DesignColors.lightBackgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors(context).brightness == Brightness.dark
                              ? DesignColors.darkStrokeColor
                              : DesignColors.lightStrokeColor,
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        Icons.arrow_back_ios_new,
                        color: colors(context).onSurface,
                        size: 20,
                      ),
                    ),
                  ),
                  Container(margin: const EdgeInsets.only(left: 16)),
                  Text(
                    Labels.addTask,
                    style: textStyles(context).titleLarge?.copyWith(
                          color: colors(context).brightness == Brightness.dark
                              ? DesignColors.darkSecondaryText
                              : DesignColors.lightSecondaryText,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ],
              ),
            ),

            // Form content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  children: [
                    // Title field
                    Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      decoration: BoxDecoration(
                        color: colors(context).brightness == Brightness.dark
                            ? DesignColors.darkBackgroundContainer
                            : DesignColors.lightBackgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors(context).brightness == Brightness.dark
                              ? DesignColors.darkStrokeColor
                              : DesignColors.lightStrokeColor,
                          width: 1,
                        ),
                      ),
                      child: TextField(
                        controller: titleController,
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                          border: InputBorder.none,
                          hintText: 'Title',
                          hintStyle: textStyles(context).bodyMedium?.copyWith(
                                color: colors(context).brightness ==
                                        Brightness.dark
                                    ? DesignColors.darkTertiaryText
                                    : DesignColors.lightTertiaryText,
                              ),
                        ),
                        style: textStyles(context).bodyMedium?.copyWith(
                              color:
                                  colors(context).brightness == Brightness.dark
                                      ? DesignColors.darkPrimaryText
                                      : DesignColors.lightPrimaryText,
                            ),
                      ),
                    ),

                    // Description field
                    Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      decoration: BoxDecoration(
                        color: colors(context).brightness == Brightness.dark
                            ? DesignColors.darkBackgroundContainer
                            : DesignColors.lightBackgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors(context).brightness == Brightness.dark
                              ? DesignColors.darkStrokeColor
                              : DesignColors.lightStrokeColor,
                          width: 1,
                        ),
                      ),
                      child: TextField(
                        controller: descriptionController,
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                          border: InputBorder.none,
                          hintText: 'Description',
                          hintStyle: textStyles(context).bodyMedium?.copyWith(
                                color:
                                    colors(context).onSurface.withOpacity(0.37),
                              ),
                        ),
                        style: textStyles(context).bodyMedium?.copyWith(
                              color: colors(context).onSurface,
                            ),
                      ),
                    ),

                    // Dropdown button 1
                    Container(
                      // padding: const EdgeInsets.fromLTRB(10, 8, 10, 6),
                      margin: const EdgeInsets.only(bottom: 8),
                      width: double.infinity,
                      child: Column(
                        children: [
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                // isDropdownOpen = !isDropdownOpen;
                              });
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 12),
                              decoration: BoxDecoration(
                                color: colors(context).brightness ==
                                        Brightness.dark
                                    ? DesignColors.darkBackgroundContainer
                                    : DesignColors.lightBackgroundContainer,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: colors(context).brightness ==
                                          Brightness.dark
                                      ? DesignColors.darkStrokeColor
                                      : DesignColors.lightStrokeColor,
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    Labels.priority,
                                    style: textStyles(context)
                                        .bodyMedium
                                        ?.copyWith(
                                          color: colors(context)
                                              .onSurface
                                              .withOpacity(0.37),
                                        ),
                                  ),
                                  Icon(
                                    Icons.keyboard_arrow_down_rounded,
                                    color: colors(context).primary,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Dropdown button 2
                    Container(
                      // padding: const EdgeInsets.fromLTRB(10, 8, 10, 6),
                      margin: const EdgeInsets.only(bottom: 14),
                      width: double.infinity,
                      child: Column(
                        children: [
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                // isDropdownOpen = !isDropdownOpen;
                              });
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 12),
                              decoration: BoxDecoration(
                                color: colors(context).brightness ==
                                        Brightness.dark
                                    ? DesignColors.darkBackgroundContainer
                                    : DesignColors.lightBackgroundContainer,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: colors(context).brightness ==
                                          Brightness.dark
                                      ? DesignColors.darkStrokeColor
                                      : DesignColors.lightStrokeColor,
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    Labels.assign,
                                    style: textStyles(context)
                                        .bodyMedium
                                        ?.copyWith(
                                          color: colors(context)
                                              .onSurface
                                              .withOpacity(0.37),
                                        ),
                                  ),
                                  Icon(
                                    Icons.keyboard_arrow_down_rounded,
                                    color: colors(context).primary,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Assign to me outlined button
                    Container(
                      margin: const EdgeInsets.only(bottom: 22),
                      width: double.infinity,
                      height: 40,
                      child: OutlinedButton(
                        style: OutlinedButton.styleFrom(
                          foregroundColor: colors(context).primary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          side: BorderSide(
                            color: colors(context).primary,
                            width: 1,
                          ),
                          padding: const EdgeInsets.symmetric(
                              vertical: 10, horizontal: 16),
                          // minimumSize: const Size.fromHeight(0),
                          backgroundColor: Colors.transparent,
                        ),
                        onPressed: () {},
                        child: Align(
                          alignment: Alignment.center,
                          child: Text(
                            Labels.assignTo,
                            style: textStyles(context).bodyMedium?.copyWith(
                                  color: colors(context).primary,
                                ),
                          ),
                        ),
                      ),
                    ),

                    // File upload area
                    InkWell(
                      onTap: _pickFile,
                      child: DottedBorder(
                        borderType: BorderType.RRect,
                        radius: const Radius.circular(8),
                        color: colors(context).outline.withOpacity(0.18),
                        dashPattern: const [6, 4],
                        strokeWidth: 1.5,
                        child: Container(
                          decoration: BoxDecoration(
                            color: colors(context).brightness == Brightness.dark
                                ? DesignColors.darkBackgroundContainer
                                : DesignColors.lightBackgroundContainer,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 24),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: colors(context).surfaceContainer,
                                  shape: BoxShape.circle,
                                ),
                                child: Center(
                                  child: SvgPicture.asset(
                                    'assets/icons/workspace_screen/upload.svg',
                                    colorFilter: ColorFilter.mode(
                                      colors(context).primary,
                                      BlendMode.srcIn,
                                    ),
                                    width: 24,
                                    height: 24,
                                  ),
                                ),
                              ),
                              Container(height: 12),
                              Text(
                                Labels.clickToUpload,
                                style: textStyles(context).bodyMedium?.copyWith(
                                      color: colors(context).primary,
                                    ),
                              ),
                              Container(height: 4),
                              Text(
                                Labels.maxFileSize,
                                style: textStyles(context).bodySmall?.copyWith(
                                      color: colors(context).brightness ==
                                              Brightness.dark
                                          ? DesignColors.darkSecondaryText
                                          : DesignColors.lightSecondaryText,
                                      fontSize: 12,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    const Spacer(),

                    // Add button
                    Container(
                      margin: const EdgeInsets.only(bottom: 24),
                      width: double.infinity,
                      height: 56,
                      child: ElevatedButton(
                        onPressed: _handleAddTask,
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              colors(context).brightness == Brightness.dark
                                  ? DesignColors.darkPrimary
                                  : DesignColors.lightPrimary,
                          foregroundColor: colors(context).onPrimary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                        child: Text(
                          Labels.save,
                          style: textStyles(context).labelLarge?.copyWith(
                                color: colors(context).onPrimary,
                                fontSize: 20,
                                fontWeight: FontWeight.w500,
                              ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
