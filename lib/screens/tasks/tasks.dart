import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/tasks/components/create_project.dart';
import 'package:ako_basma/screens/tasks/components/create_task.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ako_basma/screens/tasks/components/project_selection_dialog.dart';
import 'package:ako_basma/screens/home/<USER>/components/task_card.dart';

class Tasks extends StatefulWidget {
  const Tasks({super.key});

  @override
  State<Tasks> createState() => _TasksState();
}

class _TasksState extends State<Tasks> {
  bool _hasProject = false;
  bool _hasTasks = false;
  int _selectedProjectIndex = 0;
  final List<String> _projectList = [
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>ko <PERSON>',
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildAppBar(context),
        Expanded(
          child: _buildBody(context),
        ),
      ],
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return Container(
      color: colors(context).surfaceContainer,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppBar(
            backgroundColor: colors(context).surfaceContainer,
            title: Row(
              children: [
                GestureDetector(
                  onTap: () async {
                    final selected = await showDialog<int>(
                      context: context,
                      builder: (context) => ProjectSelectionDialog(
                        selectedIndex: _selectedProjectIndex,
                        projects: _projectList,
                        onSelected: (index) {
                          setState(() {
                            _selectedProjectIndex = index;
                            // Update project selection logic here
                          });
                        },
                      ),
                    );
                    // Optionally handle the selected value here if needed
                  },
                  child: Text(
                    _hasProject ? 'Project Name' : Labels.allProjects,
                    style: TextStyle(
                      color: colors(context).brightness == Brightness.dark
                          ? DesignColors.darkPrimaryText
                          : DesignColors.lightPrimaryText,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(left: 4),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: colors(context).primary.withOpacity(0.87),
                  size: 20,
                ),
              ],
            ),
            titleSpacing: 0,
            leadingWidth: 20,
            leading: Container(
              margin: const EdgeInsets.only(left: 20),
            ),
            actions: [
              Container(
                margin: const EdgeInsets.only(right: 14, top: 6),
                decoration: BoxDecoration(
                  color: colors(context).brightness == Brightness.dark
                      ? DesignColors.darkBackgroundContainer
                      : DesignColors.lightBackgroundContainer,
                  borderRadius: BorderRadius.circular(6.67),
                  border: Border.all(
                    color: colors(context).brightness == Brightness.dark
                        ? DesignColors.darkStrokeColor
                        : DesignColors.lightStrokeColor,
                    width: 1,
                  ),
                ),
                child: IconButton(
                  onPressed: () => _hasProject
                      ? _showCreateTaskPopup(context)
                      : _showCreateProjectPopup(context),
                  icon: SvgPicture.asset(
                    'assets/icons/tasks_screen/add.svg',
                    colorFilter: ColorFilter.mode(
                      colors(context).primary,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
            ],
          ),
          Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.only(left: 20, bottom: 8),
            child: Text(
              _hasProject ? 'Manage Tasks' : Labels.manageProjects,
              style: TextStyle(
                color: colors(context).brightness == Brightness.dark
                    ? DesignColors.darkTertiaryText
                    : DesignColors.lightTertiaryText,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showCreateProjectPopup(BuildContext context) {
    showAdaptivePopup(
      context,
      (ctx, sc) => CreateProject(
        onBack: () => Navigator.pop(ctx),
        onProjectCreated: () {
          setState(() {
            _hasProject = true;
          });
        },
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );
  }

  void _showCreateTaskPopup(BuildContext context) {
    showAdaptivePopup(
      context,
      (ctx, sc) => CreateTask(
        onBack: () => Navigator.pop(ctx),
        onTaskCreated: () {
          setState(() {
            _hasTasks = true;
          });
        },
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );
  }

  Widget _buildBody(BuildContext context) {
    if (!_hasProject) {
      // Show empty state for no projects
      return Container(
        color: colors(context).surfaceContainer,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Empty state icon
              SvgPicture.asset(
                'assets/images/server.svg',
                width: 64,
                height: 64,
                colorFilter: ColorFilter.mode(
                  colors(context).brightness == Brightness.dark
                      ? DesignColors.darkTertiaryText
                      : DesignColors
                          .lightTertiaryText, // TODO - ask tushar sir whether to give color to an image or not?
                  BlendMode.srcIn,
                ),
              ),
              Container(
                padding: const EdgeInsets.only(top: 16),
              ),
              Text(
                Labels.noProjectFound,
                style: TextStyle(
                  color: colors(context).brightness == Brightness.dark
                      ? DesignColors.darkPrimaryText
                      : DesignColors.lightPrimaryText,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Container(
                padding: const EdgeInsets.only(top: 8),
              ),
              Text(
                Labels.pleaseCreateAProject,
                style: TextStyle(
                  color: colors(context).brightness == Brightness.dark
                      ? DesignColors.darkSecondaryText
                      : DesignColors.lightSecondaryText,
                  fontSize: 14,
                ),
              ),
              Container(
                padding: const EdgeInsets.only(top: 24),
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 110),
                height: 40,
                width: 181,
                child: ElevatedButton.icon(
                  onPressed: () => _showCreateProjectPopup(context),
                  icon: SvgPicture.asset(
                    'assets/icons/tasks_screen/add.svg',
                    width: 24,
                    height: 24,
                    colorFilter: ColorFilter.mode(
                      colors(context).onPrimary,
                      BlendMode.srcIn,
                    ),
                  ),
                  label: Text(
                    Labels.createProject,
                    style: TextStyle(
                      color: colors(context).onPrimary,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        colors(context).brightness == Brightness.dark
                            ? DesignColors.darkPrimary
                            : DesignColors.lightPrimary,
                    foregroundColor: colors(context).onPrimary,
                    padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else if (!_hasTasks) {
      // Show empty state for no tasks
      return Container(
        color: colors(context).surfaceContainer,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Empty state icon
              SvgPicture.asset(
                'assets/images/server.svg',
                width: 64,
                height: 64,
                color: colors(context).onSurface.withOpacity(0.6),
              ),
              const SizedBox(height: 16),
              Text(
                'No Tasks Found',
                style: TextStyle(
                  color: colors(context).inverseSurface.withOpacity(0.87),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Please create a task to get started',
                style: TextStyle(
                  color: colors(context).inverseSurface.withOpacity(0.60),
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 24),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 80),
                child: ElevatedButton.icon(
                  onPressed: () => _showCreateTaskPopup(context),
                  icon: Icon(Icons.add,
                      size: 20, color: colors(context).onPrimary),
                  label: Text(
                    'Create Task',
                    style: TextStyle(
                      color: colors(context).onPrimary,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colors(context).primary,
                    foregroundColor: colors(context).onPrimary,
                    padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                    minimumSize: const Size(double.infinity, 48),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      // Show tasks list
      return Container(
        color: colors(context).surfaceContainer,
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: 3, // For now, show 3 sample tasks
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: const TaskCard(),
            );
          },
        ),
      );
    }
  }
}
