import 'package:ako_basma/screens/auth/otp_screen.dart';
import 'package:ako_basma/screens/home/<USER>/clock_in/clock_in_details.dart';
import 'package:ako_basma/screens/profile/profile_screen.dart';
import 'package:ako_basma/screens/tasks/tasks.dart';

import 'package:ako_basma/screens/verify/verify.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../components/scaffold/scaffold.dart';
import '../../providers/auth/login_state.dart';
import '../../screens/auth/auth.dart';
import '../../screens/home/<USER>';
import '../../screens/chat/screen/chat.dart';
part 'router.g.dart';

@riverpod
GoRouter router(RouterRef ref) {
  final rootNavigatorKey = GlobalKey<NavigatorState>();

  final authState = ValueNotifier<LoginStates>(ref.read(loginStateProvider));
  ref
    ..onDispose(authState.dispose)
    ..listen(
      loginStateProvider.select((value) => value),
      (_, next) {
        authState.value = next;
      },
    );

  final navigationItems = [
    //Modify the paths and body component.
    NavigationItem(
      path: '/home',
      body: (context, state) => const HomeScreen(),
      routes: [],
    ),
    NavigationItem(
      path: '/chat',
      body: (context, state) => const ChatScreen(),
      routes: [],
    ),
    NavigationItem(
      path: '/tasks',
      body: (context, state) => const Tasks(),
      routes: [],
    ),
    NavigationItem(
      path: '/profile',
      body: (context, state) => const ProfileScreen(),
      routes: [],
    ),
  ];

  ///called at the top of redirector
  ///checks if the state.path is to be used as a deeplink.
  String? redirectLocation;

  final router = GoRouter(
    // initialLocation: '/login', // TODO: change to /home
    initialLocation: '/login',
    navigatorKey: rootNavigatorKey,
    refreshListenable: authState,
    redirect: (context, state) {
      if (kDebugMode) {
        print('full uri: ${state.uri}');
      }

      final loginState = authState.value;
      if ([
        LoginStates.waiting,
        LoginStates.notAllowed,
        LoginStates.expired,
        LoginStates.error,
        LoginStates.offline,
      ].contains(loginState)) {
        return '/verify';
      }

      final authenticated = authState.value == LoginStates.allowed;

      if (state.fullPath == '/' || (state.fullPath?.isEmpty ?? false)) {
        return '/home';
      }
      // if (!initState && !authenticated) return '/onboarding';
      if (state.fullPath == '/' || (state.fullPath?.isEmpty ?? false)) {
        if (authenticated && redirectLocation != null) {
          final location = redirectLocation;
          redirectLocation = null;
          return location;
        }
        return authenticated
            ? '/home'
            : '/login'; // TODO: change to /onboarding
      }

      return null;
    },
    routes: [
      // public routes
      // GoRoute(
      //   path: '/onboarding',
      //   pageBuilder: (context, state) => const MaterialPage(
      //     child: OnboardingScreen(),
      //   ),
      // ),
      GoRoute(
        path: '/verify',
        pageBuilder: (context, state) => const MaterialPage(
          child: VerifyScreen(),
        ),
      ),
      GoRoute(
        path: '/login',
        pageBuilder: (context, state) {
          return const MaterialPage(
            child: LoginScreen(),
          );
        },
      ),
      GoRoute(
        path: '/otp',
        pageBuilder: (context, state) => const MaterialPage(
          child: OTPScreen(),
        ),
      ),
      // Add a simple onboarding route to prevent errors
      GoRoute(
        path: '/onboarding',
        pageBuilder: (context, state) => MaterialPage(
          child: Container(
            padding: const EdgeInsets.all(20),
            child: const Center(
              child: Text('Onboarding Screen'),
            ),
          ),
        ),
      ),

      ShellRoute(
        builder: (context, state, child) {
          // Determine the selected index based on the current location
          int selectedIndex = 0;
          final location = state.uri.toString();

          if (location.startsWith('/home')) {
            selectedIndex = 0;
          } else if (location.startsWith('/chat')) {
            selectedIndex = 1;
          } else if (location.startsWith('/tasks')) {
            selectedIndex = 2;
          } else if (location.startsWith('/profile')) {
            selectedIndex = 3;
          }

          return ScaffoldWithNestedNavigation(
            selectedIndex: selectedIndex,
            navigationItems: navigationItems,
            child: child,
          );
        },
        routes: [
          for (final item in navigationItems)
            GoRoute(
              path: item.path,
              pageBuilder: (context, _) {
                return CustomTransitionPage(
                  transitionsBuilder: (_, animation, __, child) {
                    return FadeTransition(
                      opacity: animation.drive(CurveTween(curve: Curves.ease)),
                      child: child,
                    );
                  },
                  child: item.body(context, _),
                );
              },
              routes: item.routes,
            ),
          GoRoute(
            path: '/clock-in-details',
            pageBuilder: (context, _) {
              return CustomTransitionPage(
                transitionsBuilder: (_, animation, __, child) {
                  return FadeTransition(
                    opacity: animation.drive(CurveTween(curve: Curves.ease)),
                    child: child,
                  );
                },
                child: const ClockInDetailsScreen(),
              );
            },
          ),
        ],
      ),
    ],
  );

  ref.onDispose(router.dispose);

  return router;
}
